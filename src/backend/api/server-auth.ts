'use server';

import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { generateToken } from '@/backend/utils/token.util';
import { getAuthService } from '@/backend/wire';
import { config } from '@/config';
import { Provider } from '@prisma/client';
import { cookies } from 'next/headers';

export async function logoutApi(): Promise<void> {
	const cookieStore = await cookies();
	cookieStore.delete(config.auth.jwtCookieName);
}

export async function providerLoginApi(provider: Provider, providerId: string): Promise<void> {
	if (!provider || !providerId || !Object.values(Provider).includes(provider)) {
		throw new ValidationError('Invalid provider or provider ID');
	}

	const authService = getAuthService();
	const user = await authService.providerLogin(provider, providerId);
	if (!user) throw new UnauthorizedError('Authentication failed');

	const token = await generateToken(user);
	if (!token) throw new Error('Token generation failed');

	const cookieStore = await cookies();
	cookieStore.set({
		name: config.auth.jwtCookieName,
		value: token,
		httpOnly: true,
		secure: config.server.env === 'production',
		sameSite: 'strict',
		path: '/',
		maxAge: config.auth.jwtExpiresIn,
	});
}

export async function developmentLoginApi(): Promise<void> {
	if (config.server.env !== 'development') {
		throw new Error('Development login is only available in development mode');
	}

	const defaultUser = config.auth.defaultUser;
	await providerLoginApi(defaultUser.provider, defaultUser.provider_id);
}
