{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "scan": "next dev --turbopack & npx react-scan@latest localhost:3000", "build": "next build", "start": "next start", "lint": "npx tsc --noEmit --skipLibCheck --project .", "p:m": "prisma migrate dev", "p:m:r": "prisma migrate reset", "p:s": "prisma studio"}, "dependencies": {"@genkit-ai/googleai": "^1.10.0", "@hookform/resolvers": "^4.1.3", "@prisma/client": "^6.8.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@telegram-apps/sdk": "^3.5.1", "@telegram-apps/sdk-react": "^3.1.2", "@telegram-apps/types": "^2.0.0", "@types/nprogress": "^0.2.3", "@types/pdfkit": "^0.13.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "docx": "^9.4.1", "framer-motion": "^12.6.2", "genkit": "^1.10.0", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.483.0", "mammoth": "^1.9.0", "next": "^15.3.2", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "node-cache": "^5.1.2", "nprogress": "^0.2.0", "openai": "^4.89.0", "pdf-parse": "^1.1.1", "pdfkit": "^0.17.0", "radix-ui": "^1.1.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-scan": "^0.3.4", "react-window": "^1.8.11", "sonner": "^2.0.1", "swr": "^2.3.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/pdf-parse": "^1.1.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-window": "^1.8.8", "dotenv": "^16.4.7", "eslint": "^9", "eslint-config-next": "15.2.3", "eslint-config-prettier": "^10.1.1", "prisma": "^6.8.2", "tailwindcss": "^4", "typescript": "^5", "typescript-eslint": "^8.28.0"}}